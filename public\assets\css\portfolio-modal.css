/* Enhanced Portfolio Modal Styles */

/* Modal backdrop blur effect */
#project-modal #modal-overlay {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* Modal container animations */
#project-modal #modal-container {
    animation: modalSlideIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes modalSlideIn {
    0% {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Image hover effects */
#modal-image {
    transition: transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#modal-image:hover {
    transform: scale(1.05);
}

/* Progress bar animation */
#modal-progress {
    transition: width 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Spec cards hover effects */
.spec-card {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.spec-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s;
}

.spec-card:hover::before {
    left: 100%;
}

.spec-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Button animations */
.modal-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.modal-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.modal-btn:hover::before {
    width: 300px;
    height: 300px;
}

/* Close button special effects */
#modal-close {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#modal-close:hover {
    transform: rotate(90deg) scale(1.1);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
}

/* Category badge glow effect */
.category-badge {
    position: relative;
    animation: categoryGlow 2s ease-in-out infinite alternate;
}

@keyframes categoryGlow {
    0% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
    }
    100% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
    }
}

/* Notification animation */
.notification-slide {
    animation: slideInRight 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slideInRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Content stagger animation */
.content-stagger {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.content-stagger.animate {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    #project-modal #modal-container {
        max-width: 90vw;
        margin: 1rem;
    }
}

@media (max-width: 768px) {
    #project-modal #modal-container {
        max-width: 95vw;
        margin: 0.5rem;
        border-radius: 1.5rem;
    }
    
    .spec-card {
        padding: 0.75rem;
    }
}

/* Loading state */
.modal-loading {
    position: relative;
}

.modal-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Smooth scrollbar for modal content */
#modal-container::-webkit-scrollbar {
    width: 6px;
}

#modal-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

#modal-container::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 3px;
}

#modal-container::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.7);
}

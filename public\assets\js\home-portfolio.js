// assets/js/home-portfolio.js

document.addEventListener('DOMContentLoaded', function() {
    // Cache DOM elements
    const filterBtns = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');
    const modal = document.getElementById('project-modal');
    const modalOverlay = document.getElementById('modal-overlay');
    const modalContainer = document.getElementById('modal-container');
    const modalClose = document.getElementById('modal-close');
    const modalImage = document.getElementById('modal-image');
    const modalTitle = document.getElementById('modal-title');
    const modalTitleOverlay = document.getElementById('modal-title-overlay');
    const modalCategory = document.getElementById('modal-category');
    const modalCategoryBadge = document.getElementById('modal-category-badge');
    const modalDescription = document.getElementById('modal-description');
    const modalContactBtn = document.getElementById('modal-contact-btn');
    const modalShareBtn = document.getElementById('modal-share-btn');
    const modalClient = document.getElementById('modal-client');
    const modalYear = document.getElementById('modal-year');
    const modalYearOverlay = document.getElementById('modal-year-overlay');
    const modalLocation = document.getElementById('modal-location');
    const modalLocationOverlay = document.getElementById('modal-location-overlay');
    const modalArea = document.getElementById('modal-area');
    const modalProgress = document.getElementById('modal-progress');
    const imageDots = document.getElementById('image-dots');

    const ANIMATION_DURATION_MS = 300;
    const ITEM_ANIMATION_DELAY_MS = 50;

    /**
     * Styles a filter button based on its active state.
     * @param {HTMLButtonElement} button - The filter button element.
     * @param {boolean} isActive - Whether the button should be styled as active.
     */
    function styleFilterButton(button, isActive) {
        if (isActive) {
            button.classList.add('active', 'bg-primary-500', 'text-white');
            button.classList.remove('bg-white', 'dark:bg-gray-700', 'text-secondary-700', 'dark:text-gray-300', 'hover:bg-primary-50', 'dark:hover:bg-gray-600');
        } else {
            button.classList.remove('active', 'bg-primary-500', 'text-white');
            button.classList.add('bg-white', 'dark:bg-gray-700', 'text-secondary-700', 'dark:text-gray-300', 'hover:bg-primary-50', 'dark:hover:bg-gray-600');
        }
    }

    /**
     * Shows or hides a portfolio item with animation.
     * @param {HTMLElement} item - The portfolio item element.
     * @param {boolean} show - Whether to show or hide the item.
     * @param {number} delay - Delay before starting the animation.
     */
    function animatePortfolioItem(item, show, delay = 0) {
        if (show) {
            item.classList.remove('hidden');
            setTimeout(() => {
                item.classList.remove('opacity-0', 'scale-95');
                item.classList.add('opacity-100', 'scale-100');
            }, delay);
        } else {
            item.classList.add('opacity-0', 'scale-95');
            setTimeout(() => {
                item.classList.add('hidden');
            }, ANIMATION_DURATION_MS); // Match transition duration
        }
    }

    /**
     * Applies the selected filter to the portfolio items.
     * @param {string} filterValue - The category to filter by ('all' or specific category).
     */
    function applyPortfolioFilter(filterValue) {
        portfolioItems.forEach(item => {
            const itemCategory = item.getAttribute('data-category');
            const matchesFilter = (filterValue === 'all' || itemCategory === filterValue);
            animatePortfolioItem(item, matchesFilter, ITEM_ANIMATION_DELAY_MS);
        });
    }

    /**
     * Initializes filter button event listeners.
     */
    function initializeFilterButtons() {
        if (filterBtns.length === 0) return;

        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                filterBtns.forEach(b => styleFilterButton(b, false)); // Deactivate all
                styleFilterButton(this, true); // Activate clicked
                
                const filterValue = this.getAttribute('data-filter');
                applyPortfolioFilter(filterValue);
            });
        });
    }

    /**
     * Opens the project details modal with enhanced animation.
     */
    function openModal() {
        if (!modal || !modalOverlay || !modalContainer) return;

        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Start progress animation
        if (modalProgress) {
            modalProgress.style.width = '0%';
            setTimeout(() => {
                modalProgress.style.width = '100%';
            }, 100);
        }

        // Staggered animation entrance
        setTimeout(() => {
            modalOverlay.classList.add('opacity-100');
            modalContainer.classList.remove('scale-90', 'opacity-0');
            modalContainer.classList.add('scale-100', 'opacity-100');

            // Add entrance animation to content elements
            const animatedElements = modalContainer.querySelectorAll('[data-animate="true"]');
            animatedElements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }, 50);
    }

    /**
     * Closes the project details modal with enhanced animation.
     */
    function closeModal() {
        if (!modal || !modalOverlay || !modalContainer || modal.classList.contains('hidden')) return;

        // Reset progress
        if (modalProgress) {
            modalProgress.style.width = '0%';
        }

        modalOverlay.classList.remove('opacity-100');
        modalContainer.classList.remove('scale-100', 'opacity-100');
        modalContainer.classList.add('scale-90', 'opacity-0');

        setTimeout(() => {
            modal.classList.add('hidden');
            document.body.style.overflow = '';
        }, 500); // Increased duration for smoother animation
    }

    /**
     * Initializes modal event listeners (close button, overlay click, Escape key, share).
     */
    function initializeModalEventListeners() {
        if (modalClose) modalClose.addEventListener('click', closeModal);
        if (modalOverlay) modalOverlay.addEventListener('click', closeModal);

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modal && !modal.classList.contains('hidden')) {
                closeModal();
            }
        });

        if (modalContactBtn) {
            modalContactBtn.addEventListener('click', closeModal);
        }

        // Share functionality
        if (modalShareBtn) {
            modalShareBtn.addEventListener('click', function() {
                const projectTitle = modalTitle ? modalTitle.textContent : 'Project';
                const projectDescription = modalDescription ? modalDescription.textContent : '';

                if (navigator.share) {
                    navigator.share({
                        title: `${projectTitle} - Antosa Architect`,
                        text: projectDescription,
                        url: window.location.href
                    }).catch(console.error);
                } else {
                    // Fallback: copy to clipboard
                    const shareText = `${projectTitle} - Antosa Architect\n${projectDescription}\n${window.location.href}`;
                    navigator.clipboard.writeText(shareText).then(() => {
                        // Show temporary notification
                        showNotification('Link berhasil disalin ke clipboard!');
                    }).catch(() => {
                        // Fallback for older browsers
                        const textArea = document.createElement('textarea');
                        textArea.value = shareText;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        showNotification('Link berhasil disalin!');
                    });
                }
            });
        }
    }

    /**
     * Shows a temporary notification
     */
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    /**
     * Populates and opens the enhanced project modal. Exposed globally.
     * @param {string} title - Project title.
     * @param {string} imageSrc - Project image URL.
     * @param {string} descriptionHTML - Project description (HTML, line breaks as <br>).
     * @param {string} category - Project category.
     * @param {string} client - Client name.
     * @param {string} year - Project year.
     * @param {string} location - Project location.
     * @param {string} area - Project area.
     */
    window.openProjectModal = function(title, imageSrc, descriptionHTML, category, client, year, location, area) {
        // Guard clauses for essential modal elements
        if (!modalTitle || !modalImage || !modalCategory || !modalDescription) {
            console.error("Essential modal elements are not found in the DOM.");
            return;
        }

        // Populate main elements
        modalTitle.textContent = title || '';
        modalImage.src = imageSrc || '';
        modalImage.alt = title || 'Project Image';
        modalCategory.textContent = category || '';
        modalDescription.innerHTML = descriptionHTML ? descriptionHTML.replace(/\n/g, '<br>') : '';

        // Populate overlay elements
        if (modalTitleOverlay) modalTitleOverlay.textContent = title || '';
        if (modalCategoryBadge) modalCategoryBadge.textContent = category || '';
        if (modalYearOverlay) modalYearOverlay.textContent = year || '';
        if (modalLocationOverlay) {
            const locationSpan = modalLocationOverlay.querySelector('span');
            if (locationSpan) locationSpan.textContent = location || '';
        }

        // Populate detail cards
        if (modalClient) modalClient.textContent = client || 'N/A';
        if (modalYear) modalYear.textContent = year || 'N/A';
        if (modalLocation) modalLocation.textContent = location || 'N/A';
        if (modalArea) modalArea.textContent = area || 'N/A';

        // Add animation attributes to content elements
        const contentElements = [
            modalTitle, modalCategory, modalDescription,
            modalClient, modalYear, modalLocation, modalArea
        ].filter(Boolean);

        contentElements.forEach((el, index) => {
            el.setAttribute('data-animate', 'true');
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
        });

        // Generate category-specific styling
        applyCategoryTheme(category);

        openModal();
    };

    /**
     * Applies category-specific theming to the modal
     * @param {string} category - Project category
     */
    function applyCategoryTheme(category) {
        const themes = {
            'Residensial': {
                gradient: 'from-blue-500/20 to-blue-600/20',
                border: 'border-blue-400/30',
                text: 'text-blue-300'
            },
            'Komersial': {
                gradient: 'from-green-500/20 to-green-600/20',
                border: 'border-green-400/30',
                text: 'text-green-300'
            },
            'Hospitality': {
                gradient: 'from-purple-500/20 to-purple-600/20',
                border: 'border-purple-400/30',
                text: 'text-purple-300'
            }
        };

        const theme = themes[category] || themes['Residensial'];

        // Apply theme to category elements
        [modalCategory, modalCategoryBadge].forEach(el => {
            if (el) {
                el.className = el.className.replace(/bg-gradient-to-r from-\w+-\d+\/\d+ to-\w+-\d+\/\d+/, `bg-gradient-to-r ${theme.gradient}`);
                el.className = el.className.replace(/border-\w+-\d+\/\d+/, theme.border);
                el.className = el.className.replace(/text-\w+-\d+/, theme.text);
            }
        });
    }

    /**
     * Animates the initial display of portfolio items.
     */
    function animateInitialPortfolioItems() {
        if (portfolioItems.length === 0) return;
        portfolioItems.forEach((item, index) => {
            // Ensure items are visible before animation starts if they were hidden by default
            item.classList.remove('hidden'); 
            animatePortfolioItem(item, true, ITEM_ANIMATION_DELAY_MS * (index + 1));
        });
    }

    // Initialize all functionalities
    initializeFilterButtons();
    initializeModalEventListeners();
    animateInitialPortfolioItems();
});

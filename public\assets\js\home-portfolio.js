// assets/js/home-portfolio.js

document.addEventListener('DOMContentLoaded', function() {
    // Cache DOM elements
    const filterBtns = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');
    const modal = document.getElementById('project-modal');
    const modalOverlay = document.getElementById('modal-overlay');
    const modalContainer = document.getElementById('modal-container');
    const modalClose = document.getElementById('modal-close');
    const modalShareBtn = document.getElementById('modal-share-btn');
    const modalContactBtn = document.getElementById('modal-contact-btn');

    const ANIMATION_DURATION_MS = 300;
    const ITEM_ANIMATION_DELAY_MS = 50;

    /**
     * Styles a filter button based on its active state.
     * @param {HTMLButtonElement} button - The filter button element.
     * @param {boolean} isActive - Whether the button should be styled as active.
     */
    function styleFilterButton(button, isActive) {
        if (isActive) {
            button.classList.add('active', 'bg-primary-500', 'text-white');
            button.classList.remove('bg-white', 'dark:bg-gray-700', 'text-secondary-700', 'dark:text-gray-300', 'hover:bg-primary-50', 'dark:hover:bg-gray-600');
        } else {
            button.classList.remove('active', 'bg-primary-500', 'text-white');
            button.classList.add('bg-white', 'dark:bg-gray-700', 'text-secondary-700', 'dark:text-gray-300', 'hover:bg-primary-50', 'dark:hover:bg-gray-600');
        }
    }

    /**
     * Shows or hides a portfolio item with animation.
     * @param {HTMLElement} item - The portfolio item element.
     * @param {boolean} show - Whether to show or hide the item.
     * @param {number} delay - Delay before starting the animation.
     */
    function animatePortfolioItem(item, show, delay = 0) {
        if (show) {
            item.classList.remove('hidden');
            setTimeout(() => {
                item.classList.remove('opacity-0', 'scale-95');
                item.classList.add('opacity-100', 'scale-100');
            }, delay);
        } else {
            item.classList.add('opacity-0', 'scale-95');
            setTimeout(() => {
                item.classList.add('hidden');
            }, ANIMATION_DURATION_MS); // Match transition duration
        }
    }

    /**
     * Applies the selected filter to the portfolio items.
     * @param {string} filterValue - The category to filter by ('all' or specific category).
     */
    function applyPortfolioFilter(filterValue) {
        portfolioItems.forEach(item => {
            const itemCategory = item.getAttribute('data-category');
            const matchesFilter = (filterValue === 'all' || itemCategory === filterValue);
            animatePortfolioItem(item, matchesFilter, ITEM_ANIMATION_DELAY_MS);
        });
    }

    /**
     * Initializes filter button event listeners.
     */
    function initializeFilterButtons() {
        if (filterBtns.length === 0) return;

        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                filterBtns.forEach(b => styleFilterButton(b, false)); // Deactivate all
                styleFilterButton(this, true); // Activate clicked
                
                const filterValue = this.getAttribute('data-filter');
                applyPortfolioFilter(filterValue);
            });
        });
    }

    /**
     * Opens the project details modal.
     */
    function openModal() {
        if (!modal || !modalOverlay || !modalContainer) return;

        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        setTimeout(() => {
            modalOverlay.classList.add('opacity-100');
            modalContainer.classList.remove('scale-95', 'opacity-0');
            modalContainer.classList.add('scale-100', 'opacity-100');
        }, 10);
    }

    /**
     * Closes the project details modal.
     */
    function closeModal() {
        if (!modal || !modalOverlay || !modalContainer || modal.classList.contains('hidden')) return;

        modalOverlay.classList.remove('opacity-100');
        modalContainer.classList.remove('scale-100', 'opacity-100');
        modalContainer.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
            modal.classList.add('hidden');
            document.body.style.overflow = '';
        }, 300);
    }

    /**
     * Initializes modal event listeners.
     */
    function initializeModalEventListeners() {
        if (modalClose) modalClose.addEventListener('click', closeModal);
        if (modalOverlay) modalOverlay.addEventListener('click', closeModal);

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modal && !modal.classList.contains('hidden')) {
                closeModal();
            }
        });

        if (modalContactBtn) {
            modalContactBtn.addEventListener('click', closeModal);
        }

        // Share functionality
        if (modalShareBtn) {
            modalShareBtn.addEventListener('click', function() {
                const projectTitle = document.getElementById('modal-title')?.textContent || 'Project';
                const shareText = `${projectTitle} - Antosa Architect\n${window.location.href}`;

                if (navigator.share) {
                    navigator.share({
                        title: `${projectTitle} - Antosa Architect`,
                        url: window.location.href
                    }).catch(console.error);
                } else {
                    navigator.clipboard.writeText(shareText).then(() => {
                        showNotification('Link berhasil disalin!');
                    }).catch(() => {
                        showNotification('Gagal menyalin link');
                    });
                }
            });
        }
    }

    /**
     * Shows a temporary notification
     */
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => notification.classList.remove('translate-x-full'), 100);
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => document.body.removeChild(notification), 300);
        }, 2000);
    }
    
    /**
     * Populates and opens the project modal.
     */
    window.openProjectModal = function(title, imageSrc, descriptionHTML, category, client, year, location, area) {
        // Get modal elements
        const elements = {
            title: document.getElementById('modal-title'),
            titleOverlay: document.getElementById('modal-title-overlay'),
            image: document.getElementById('modal-image'),
            category: document.getElementById('modal-category'),
            categoryBadge: document.getElementById('modal-category-badge'),
            description: document.getElementById('modal-description'),
            client: document.getElementById('modal-client'),
            year: document.getElementById('modal-year'),
            location: document.getElementById('modal-location'),
            locationOverlay: document.getElementById('modal-location-overlay'),
            area: document.getElementById('modal-area')
        };

        // Populate elements
        if (elements.title) elements.title.textContent = title || '';
        if (elements.titleOverlay) elements.titleOverlay.textContent = title || '';
        if (elements.image) {
            elements.image.src = imageSrc || '';
            elements.image.alt = title || 'Project Image';
        }
        if (elements.category) elements.category.textContent = category || '';
        if (elements.categoryBadge) elements.categoryBadge.textContent = category || '';
        if (elements.description) elements.description.innerHTML = descriptionHTML ? descriptionHTML.replace(/\n/g, '<br>') : '';
        if (elements.client) elements.client.textContent = client || 'N/A';
        if (elements.year) elements.year.textContent = year || 'N/A';
        if (elements.location) elements.location.textContent = location || 'N/A';
        if (elements.area) elements.area.textContent = area || 'N/A';

        // Update location overlay
        if (elements.locationOverlay) {
            const locationSpan = elements.locationOverlay.querySelector('span');
            if (locationSpan) locationSpan.textContent = location || '';
        }

        openModal();
    };

    // Initialize functionalities
    initializeFilterButtons();
    initializeModalEventListeners();
});
